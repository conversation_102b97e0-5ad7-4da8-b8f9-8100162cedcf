<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../models/RppQuestion.php';
require_once '../models/VisualQuestion.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Get parameters
$question_id = $_GET['question_id'] ?? 0;
$question_type = $_GET['question_type'] ?? 'rpp_question';

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Get question data
$rppQuestion = new RppQuestion();
$question_data = $rppQuestion->getOne($question_id);

if (!$question_data) {
    $_SESSION['error'] = "Soal tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

// Verify ownership through RPP
$rpp = new Rpp();
$rpp_data = $rpp->getOne($question_data['rpp_id']);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Anda tidak memiliki akses ke soal ini.";
    header("Location: generate_questions.php");
    exit();
}

// Get visual content
$visualQuestion = new VisualQuestion();
$visual_data = $visualQuestion->getByQuestionId($question_id, $question_type);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-eye"></i> Viewer Soal Visual
                        </h5>
                        <small class="text-muted">
                            RPP: <?= htmlspecialchars($rpp_data['tema_subtema']) ?> - 
                            <?= htmlspecialchars($rpp_data['nama_mapel']) ?>
                        </small>
                    </div>
                    <div>
                        <a href="questions_list.php?rpp_id=<?= $question_data['rpp_id'] ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Question Information -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h6 class="fw-bold">Teks Soal:</h6>
                            <div class="p-3 bg-light rounded">
                                <?= nl2br(htmlspecialchars($question_data['question_text'])) ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="fw-bold">Informasi Soal:</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>Jenis:</td>
                                    <td>
                                        <span class="badge bg-<?= $question_data['question_type'] === 'multiple_choice' ? 'primary' : 'success' ?>">
                                            <?= $question_data['question_type'] === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay' ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Tingkat:</td>
                                    <td>
                                        <?php
                                        $difficulty_labels = [
                                            'regular' => 'Regular',
                                            'hots_easy' => 'HOTS Mudah',
                                            'hots_medium' => 'HOTS Sedang',
                                            'hots_hard' => 'HOTS Sulit'
                                        ];
                                        echo $difficulty_labels[$question_data['difficulty_level']] ?? 'Regular';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Visual:</td>
                                    <td>
                                        <?php if ($visual_data): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> Ada
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-times"></i> Tidak Ada
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Multiple Choice Options -->
                    <?php if ($question_data['question_type'] === 'multiple_choice' && $question_data['options']): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold">Pilihan Jawaban:</h6>
                                <div class="row">
                                    <?php
                                    $options = json_decode($question_data['options'], true);
                                    if ($options):
                                        foreach ($options as $option):
                                    ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="p-2 border rounded <?= strpos($option, $question_data['correct_answer'] . '.') === 0 ? 'bg-success bg-opacity-10 border-success' : '' ?>">
                                                <?= htmlspecialchars($option) ?>
                                                <?php if (strpos($option, $question_data['correct_answer'] . '.') === 0): ?>
                                                    <i class="fas fa-check text-success ms-2"></i>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Visual Content -->
                    <?php if ($visual_data): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold">
                                    Konten Visual 
                                    <span class="badge bg-info ms-2">
                                        <?= ucfirst(str_replace('_', ' ', $visual_data['visual_type'])) ?>
                                    </span>
                                </h6>
                                
                                <!-- Visual Configuration Info -->
                                <div class="mb-3">
                                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#visualConfig">
                                        <i class="fas fa-cog"></i> Lihat Konfigurasi Visual
                                    </button>
                                </div>
                                
                                <div class="collapse" id="visualConfig">
                                    <div class="card card-body mb-3">
                                        <h6>Konfigurasi Visual:</h6>
                                        <pre class="bg-light p-2 rounded"><code><?= json_encode(json_decode($visual_data['visual_config'], true), JSON_PRETTY_PRINT) ?></code></pre>
                                        
                                        <?php if ($visual_data['generation_metadata']): ?>
                                            <h6 class="mt-3">Metadata Generasi:</h6>
                                            <pre class="bg-light p-2 rounded"><code><?= json_encode(json_decode($visual_data['generation_metadata'], true), JSON_PRETTY_PRINT) ?></code></pre>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Visual Content Display -->
                                <div class="border rounded p-3" style="min-height: 400px;">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Preview Visual:</h6>
                                        <div>
                                            <button class="btn btn-sm btn-primary" onclick="exportVisualContent()">
                                                <i class="fas fa-download"></i> Export Gambar
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="toggleFullscreen()">
                                                <i class="fas fa-expand"></i> Fullscreen
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Visual Content Frame -->
                                    <div id="visualContentFrame" style="width: 100%; height: 500px; border: 1px solid #ddd; border-radius: 4px;">
                                        <iframe id="visualIframe" 
                                                src="data:text/html;charset=utf-8,<?= urlencode($visual_data['html_content']) ?>" 
                                                style="width: 100%; height: 100%; border: none; border-radius: 4px;">
                                        </iframe>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <a href="edit_question.php?id=<?= $question_id ?>" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Soal
                                </a>
                                
                                <?php if ($visual_data): ?>
                                    <button class="btn btn-info" onclick="regenerateVisual()">
                                        <i class="fas fa-sync"></i> Regenerate Visual
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-success" onclick="addVisualContent()">
                                        <i class="fas fa-plus"></i> Tambah Visual
                                    </button>
                                <?php endif; ?>
                                
                                <button class="btn btn-danger" onclick="deleteQuestion()" data-question-id="<?= $question_id ?>">
                                    <i class="fas fa-trash"></i> Hapus Soal
                                </button>
                                
                                <a href="questions_list.php?rpp_id=<?= $question_data['rpp_id'] ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-list"></i> Daftar Soal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fullscreen Modal -->
<div class="modal fade" id="fullscreenModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Visual Content - Fullscreen</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <iframe id="fullscreenIframe" style="width: 100%; height: 100%; border: none;"></iframe>
            </div>
        </div>
    </div>
</div>

<script>
function exportVisualContent() {
    // Get the iframe content and trigger export
    const iframe = document.getElementById('visualIframe');
    
    try {
        // Try to call export function in iframe
        iframe.contentWindow.postMessage({action: 'export'}, '*');
    } catch (e) {
        // Fallback: screenshot the iframe
        html2canvas(document.getElementById('visualContentFrame')).then(canvas => {
            const link = document.createElement('a');
            link.download = 'visual-question-<?= $question_id ?>.png';
            link.href = canvas.toDataURL();
            link.click();
        });
    }
}

function toggleFullscreen() {
    const iframe = document.getElementById('visualIframe');
    const fullscreenIframe = document.getElementById('fullscreenIframe');
    
    fullscreenIframe.src = iframe.src;
    
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    modal.show();
}

function regenerateVisual() {
    if (confirm('Apakah Anda yakin ingin regenerate konten visual? Konten visual lama akan diganti.')) {
        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Regenerating...';
        btn.disabled = true;
        
        // AJAX call to regenerate visual
        fetch('ajax_regenerate_visual.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question_id: <?= $question_id ?>,
                question_type: '<?= $question_type ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Gagal regenerate visual: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

function addVisualContent() {
    // Redirect to visual content creation page
    window.location.href = 'add_visual_content.php?question_id=<?= $question_id ?>&question_type=<?= $question_type ?>';
}

function deleteQuestion() {
    if (confirm('Apakah Anda yakin ingin menghapus soal ini? Tindakan ini tidak dapat dibatalkan.')) {
        // AJAX call to delete question
        fetch('ajax_delete_question.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question_id: <?= $question_id ?>,
                question_type: '<?= $question_type ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Soal berhasil dihapus');
                window.location.href = 'questions_list.php?rpp_id=<?= $question_data['rpp_id'] ?>';
            } else {
                alert('Gagal menghapus soal: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

// Listen for messages from iframe
window.addEventListener('message', function(event) {
    if (event.data.action === 'export') {
        // Handle export request from iframe
        console.log('Export request received from iframe');
    }
});

// Auto-resize iframe based on content
document.getElementById('visualIframe').onload = function() {
    try {
        const iframe = this;
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        
        // Try to adjust height based on content
        const height = iframeDoc.body.scrollHeight;
        if (height > 0) {
            iframe.style.height = Math.max(500, height) + 'px';
        }
    } catch (e) {
        // Cross-origin restrictions - keep default height
        console.log('Cannot access iframe content for auto-resize');
    }
};
</script>

<!-- Include html2canvas for screenshot fallback -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<?php require_once '../template/footer.php'; ?>
