<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contoh Soal Kimia - Struktur Molekul 3D</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.0.4/3Dmol-min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .question-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .question-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 3px solid #9b59b6;
            padding-bottom: 10px;
        }
        .molecule-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .viewer-panel {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .info-panel {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 15px 0;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 2px;
        }
        .btn-primary { background-color: #3498db; color: white; }
        .btn-success { background-color: #27ae60; color: white; }
        .btn-warning { background-color: #f39c12; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
        .molecule-info {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .property-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .property-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #bdc3c7;
        }
        .property-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .property-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .formula-section {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #9b59b6;
        }
        .atom-legend {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .atom-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid #ddd;
        }
        .atom-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #333;
        }
        @media (max-width: 768px) {
            .molecule-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="question-container">
        <div class="question-title">
            🧪 Soal Kimia: Analisis Struktur Molekul 3D
        </div>
        
        <div class="question-text">
            <strong>Soal:</strong> Perhatikan struktur molekul kafein (C₈H₁₀N₄O₂) dalam representasi 3D di bawah ini. 
            Berdasarkan struktur tersebut, analisis dan tentukan:
            <ol>
                <li>Jenis ikatan yang terdapat dalam molekul</li>
                <li>Geometri molekul di sekitar atom karbon</li>
                <li>Kepolaran molekul</li>
                <li>Jenis gaya antarmolekul yang mungkin terjadi</li>
            </ol>
        </div>

        <div class="formula-section">
            <h4>☕ Tentang Kafein (C₈H₁₀N₄O₂):</h4>
            <p>Kafein adalah alkaloid yang ditemukan dalam kopi, teh, dan cokelat. Molekul ini memiliki struktur heterosiklik 
            dengan dua cincin yang menyatu (purin) dan mengandung beberapa atom nitrogen yang memberikan sifat basa lemah.</p>
            
            <div class="atom-legend">
                <div class="atom-item">
                    <div class="atom-color" style="background-color: #909090;"></div>
                    <span>Karbon (C)</span>
                </div>
                <div class="atom-item">
                    <div class="atom-color" style="background-color: #FFFFFF; border: 2px solid #333;"></div>
                    <span>Hidrogen (H)</span>
                </div>
                <div class="atom-item">
                    <div class="atom-color" style="background-color: #3050F8;"></div>
                    <span>Nitrogen (N)</span>
                </div>
                <div class="atom-item">
                    <div class="atom-color" style="background-color: #FF0D0D;"></div>
                    <span>Oksigen (O)</span>
                </div>
            </div>
        </div>

        <div class="molecule-container">
            <div class="viewer-panel">
                <h5>🔬 Visualisasi Molekul 3D</h5>
                <div id="molecule-viewer" style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 5px;"></div>
                
                <div class="controls">
                    <button class="btn btn-primary" onclick="setStyle('stick')">Stick</button>
                    <button class="btn btn-success" onclick="setStyle('sphere')">Ball & Stick</button>
                    <button class="btn btn-warning" onclick="setStyle('cartoon')">Cartoon</button>
                    <button class="btn btn-info" onclick="toggleSpin()">🔄 Rotasi</button>
                    <button class="btn btn-secondary" onclick="resetView()">Reset View</button>
                </div>
            </div>
            
            <div class="info-panel">
                <h5>📊 Informasi Molekul</h5>
                <div id="bond-analysis" style="height: 350px; overflow-y: auto;">
                    <div class="property-grid">
                        <div class="property-item">
                            <div class="property-value">194.19</div>
                            <div class="property-label">Massa Molekul (g/mol)</div>
                        </div>
                        <div class="property-item">
                            <div class="property-value">8</div>
                            <div class="property-label">Atom Karbon</div>
                        </div>
                        <div class="property-item">
                            <div class="property-value">10</div>
                            <div class="property-label">Atom Hidrogen</div>
                        </div>
                        <div class="property-item">
                            <div class="property-value">4</div>
                            <div class="property-label">Atom Nitrogen</div>
                        </div>
                        <div class="property-item">
                            <div class="property-value">2</div>
                            <div class="property-label">Atom Oksigen</div>
                        </div>
                        <div class="property-item">
                            <div class="property-value">25</div>
                            <div class="property-label">Total Ikatan</div>
                        </div>
                    </div>
                    
                    <h6 style="margin-top: 20px;">🔗 Analisis Ikatan:</h6>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><strong>Ikatan C-C:</strong> 8 ikatan (tunggal dan aromatik)</li>
                        <li><strong>Ikatan C-H:</strong> 10 ikatan tunggal</li>
                        <li><strong>Ikatan C-N:</strong> 8 ikatan (tunggal dan aromatik)</li>
                        <li><strong>Ikatan C=O:</strong> 2 ikatan rangkap</li>
                        <li><strong>Ikatan N-H:</strong> 0 ikatan</li>
                    </ul>
                    
                    <h6 style="margin-top: 15px;">⚡ Sifat Molekul:</h6>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><strong>Kepolaran:</strong> Polar (karena C=O dan N)</li>
                        <li><strong>Hibridisasi C:</strong> sp² (aromatik)</li>
                        <li><strong>Geometri:</strong> Planar di cincin</li>
                        <li><strong>Gaya antarmolekul:</strong> Van der Waals, dipol-dipol</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="molecule-info">
            <h4>🎯 Analisis Jawaban:</h4>
            <div id="analysis-content">
                <p><strong>1. Jenis Ikatan dalam Molekul:</strong></p>
                <ul>
                    <li>Ikatan kovalen tunggal (C-C, C-H, C-N)</li>
                    <li>Ikatan kovalen rangkap dua (C=O)</li>
                    <li>Ikatan aromatik dalam cincin purin</li>
                </ul>
                
                <p><strong>2. Geometri Molekul:</strong></p>
                <ul>
                    <li>Atom karbon dalam cincin: hibridisasi sp², geometri trigonal planar</li>
                    <li>Atom karbon metil: hibridisasi sp³, geometri tetrahedral</li>
                    <li>Keseluruhan molekul relatif planar karena sistem aromatik</li>
                </ul>
                
                <p><strong>3. Kepolaran Molekul:</strong></p>
                <ul>
                    <li>Molekul bersifat polar karena adanya gugus C=O dan atom N</li>
                    <li>Distribusi elektron tidak simetris</li>
                    <li>Memiliki momen dipol netto</li>
                </ul>
                
                <p><strong>4. Gaya Antarmolekul:</strong></p>
                <ul>
                    <li>Gaya van der Waals (London dispersion forces)</li>
                    <li>Interaksi dipol-dipol karena kepolaran</li>
                    <li>Kemungkinan ikatan hidrogen lemah dengan pelarut polar</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-primary" onclick="exportMolecule()">
                📥 Export Struktur 3D
            </button>
            <button class="btn btn-success" onclick="exportAnalysis()">
                📄 Export Analisis Lengkap
            </button>
        </div>
    </div>

    <script>
        let viewer;
        let isSpinning = false;
        
        // Caffeine molecule structure in SDF format (simplified)
        const caffeineStructure = `
  Mrv2014 01012024

 24 25  0  0  0  0            999 V2000
   -1.2990    0.7500    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
   -2.0135    0.3375    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
   -2.0135   -0.4875    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
   -1.2990   -0.9000    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
   -0.5845   -0.4875    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
   -0.5845    0.3375    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.1300    0.7500    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
    0.8445    0.3375    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.8445   -0.4875    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
    0.1300   -0.9000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.1300   -1.7250    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
    1.5590    0.7500    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
   -2.7280   -0.9000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    1.5590   -0.9000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.1300    1.5750    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
   -1.2990    1.5750    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
   -2.7280    0.7500    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
   -3.4425   -0.4875    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
   -2.7280   -1.7250    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
   -3.4425   -1.3125    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
    2.2735   -0.4875    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
    1.5590   -1.7250    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
    2.2735   -1.3125    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
   -0.5845    1.9875    0.0000 H   0  0  0  0  0  0  0  0  0  0  0  0
  1  2  1  0  0  0  0
  2  3  1  0  0  0  0
  3  4  2  0  0  0  0
  4  5  1  0  0  0  0
  5  6  2  0  0  0  0
  6  1  1  0  0  0  0
  6  7  1  0  0  0  0
  7  8  1  0  0  0  0
  8  9  1  0  0  0  0
  9 10  1  0  0  0  0
 10  5  1  0  0  0  0
 10 11  2  0  0  0  0
  8 12  2  0  0  0  0
  3 13  1  0  0  0  0
  9 14  1  0  0  0  0
  7 15  1  0  0  0  0
M  END
`;

        function initializeMoleculeViewer() {
            const element = document.getElementById('molecule-viewer');
            const config = { backgroundColor: 'white' };
            viewer = $3Dmol.createViewer(element, config);
            
            // Add caffeine molecule (using a simplified structure)
            viewer.addModel(caffeineStructure, 'sdf');
            
            // Set initial style
            viewer.setStyle({}, {
                stick: { colorscheme: 'Jmol', radius: 0.2 },
                sphere: { colorscheme: 'Jmol', scale: 0.3 }
            });
            
            viewer.zoomTo();
            viewer.render();
            
            // Start with gentle rotation
            viewer.spin(true);
            isSpinning = true;
        }

        function setStyle(styleType) {
            viewer.removeAllModels();
            viewer.addModel(caffeineStructure, 'sdf');
            
            switch(styleType) {
                case 'stick':
                    viewer.setStyle({}, {
                        stick: { colorscheme: 'Jmol', radius: 0.2 }
                    });
                    break;
                case 'sphere':
                    viewer.setStyle({}, {
                        stick: { colorscheme: 'Jmol', radius: 0.15 },
                        sphere: { colorscheme: 'Jmol', scale: 0.25 }
                    });
                    break;
                case 'cartoon':
                    viewer.setStyle({}, {
                        stick: { colorscheme: 'Jmol', radius: 0.3 },
                        sphere: { colorscheme: 'Jmol', scale: 0.4 }
                    });
                    break;
            }
            
            viewer.render();
        }

        function toggleSpin() {
            if (isSpinning) {
                viewer.spin(false);
                isSpinning = false;
            } else {
                viewer.spin(true);
                isSpinning = true;
            }
        }

        function resetView() {
            viewer.zoomTo();
            viewer.render();
        }

        function exportMolecule() {
            // Export current view as PNG
            viewer.pngURI(function(uri) {
                const link = document.createElement('a');
                link.download = 'kafein-struktur-3d.png';
                link.href = uri;
                link.click();
            });
        }

        function exportAnalysis() {
            alert('Fitur export analisis lengkap akan segera tersedia!');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for 3Dmol to load
            setTimeout(initializeMoleculeViewer, 500);
        });

        // Listen for export messages from parent
        window.addEventListener('message', function(event) {
            if (event.data.action === 'export') {
                exportMolecule();
            }
        });
    </script>
</body>
</html>
