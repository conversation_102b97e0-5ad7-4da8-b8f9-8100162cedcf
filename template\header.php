<?php
ob_start();
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../models/PeriodeAktif.php';
require_once __DIR__ . '/../models/Guru.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Perpustakaan.php';

if(!isset($_SESSION['user_id'])) {
    header("Location: /absen/login.php");
    exit();
}

// Check if user is wali kelas
$is_wali_kelas = false;
$is_pustakawan = false;
if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'guru') {
    $user = new User();
    $guru = new Guru();
    $perpustakaan = new Perpustakaan();
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if ($guru_id) {
        $is_wali_kelas = $guru->isWaliKelas($guru_id);

        // Cek apakah guru adalah pustakawan aktif
        $pustakawan_aktif = $perpustakaan->getPustakawan();
        $id_pustakawan_aktif = array_column($pustakawan_aktif, 'id_guru');
        $is_pustakawan = in_array($guru_id, $id_pustakawan_aktif);
    }
}

// Get active period
$active_period = new PeriodeAktif();
$active_period->getActive();

// If no active period, try to set default
if(!$active_period->id) {
    $active_period->semester = PeriodeAktif::getCurrentSemester();
    $active_period->tahun_ajaran = PeriodeAktif::getCurrentTahunAjaran();
    $active_period->setActive();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIHADIR | Sistem Informasi Kehadiran Siswa</title>

    <!-- jQuery first -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <style>
        :root {
            --sidebar-width: 250px;
            --topbar-height: 60px;
            --primary-color: #4e73df;
            --secondary-color: #858796;
        }

        body {
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: #fff;
            border-right: 1px solid rgba(0,0,0,.125);
            transition: all 0.3s;
            z-index: 1000;
            overflow-y: auto;
        }

        #sidebar::-webkit-scrollbar {
            width: 6px;
        }

        #sidebar::-webkit-scrollbar-track {
            background: #f8f9fc;
        }

        #sidebar::-webkit-scrollbar-thumb {
            background-color: var(--secondary-color);
            border-radius: 3px;
        }

        #sidebar::-webkit-scrollbar-thumb:hover {
            background-color: var(--primary-color);
        }

        #sidebar.collapsed {
            margin-left: calc(-1 * var(--sidebar-width));
        }

        .sidebar-header {
            padding: 1rem;
            background: var(--primary-color);
            color: white;
            text-align: center;
        }

        .sidebar-header .logo-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .sidebar-header small {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .nav-link {
            color: var(--secondary-color);
            padding: 0.8rem 1.5rem;
            transition: all 0.3s;
        }

        .nav-link:hover {
            color: var(--primary-color);
            background: rgba(78,115,223,0.1);
        }

        .nav-link i {
            width: 25px;
        }

        /* Collapsible menu styles */
        .nav-group {
            position: relative;
        }

        .nav-group-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
        }

        .nav-group-header i.fa-chevron-down {
            transition: transform 0.3s;
        }

        .nav-group.collapsed i.fa-chevron-down {
            transform: rotate(-90deg);
        }

        .nav-group-items {
            padding-left: 1.5rem;
            max-height: 1000px;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .nav-group.collapsed .nav-group-items {
            max-height: 0;
        }

        .nav-group-items .nav-link {
            padding-left: 1rem;
            font-size: 0.9rem;
        }

        #content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
            min-height: 100vh;
        }

        #content.expanded {
            margin-left: 0;
        }

        .topbar {
            height: var(--topbar-height);
            background: white;
            border-bottom: 1px solid rgba(0,0,0,.125);
            display: flex;
            align-items: center;
            padding: 0 1rem;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .menu-toggle {
            cursor: pointer;
            padding: 0.5rem;
            display: none;
        }

        @media (max-width: 768px) {
            #sidebar {
                margin-left: calc(-1 * var(--sidebar-width));
            }

            #sidebar.active {
                margin-left: 0;
            }

            #content {
                margin-left: 0;
            }

            .menu-toggle {
                display: block;
            }

            .topbar {
                position: fixed;
                width: 100%;
                top: 0;
            }

            #content {
                padding-top: calc(var(--topbar-height) + 20px);
            }
        }

        .alert {
            background-color: transparent !important;
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da !important;
            border: 1px solid #f5c6cb !important;
            padding: 0.75rem 1.25rem !important;
            margin-bottom: 1rem !important;
            border-radius: 0.25rem;
        }

        .form-control:invalid,
        .form-select:invalid {
            background-color: #fff !important;
            border-color: #ced4da !important;
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545 !important;
            background-color: #fff !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav id="sidebar">
        <div class="sidebar-header">
            <div class="logo-icon">
                <i class="fas fa-user-clock"></i>
            </div>
            <h4 class="m-0">SIHADIR</h4>
            <small class="d-block text-light">Sistem Informasi Kehadiran Siswa</small>
        </div>
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/absen/">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </li>

                <?php if ($_SESSION['role'] === 'admin'): ?>
                <!-- Periode Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-calendar-check"></i> Periode
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                    <a class="nav-link" href="/absen/tahun_ajaran/">
                            <i class="fas fa-calendar"></i> Tahun Ajaran
                        </a>
                        <a class="nav-link" href="/absen/periode/">
                            <i class="fas fa-clock"></i> Periode
                        </a>
                        <!---
                        <a class="nav-link" href="/absen/periode_aktif/">
                            <i class="fas fa-calendar-check"></i> Periode Aktif
                        </a>
                        --->
                    </div>
                </li>

                <!-- Data Master Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-database"></i> Data Master
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a class="nav-link" href="/absen/guru/">
                            <i class="fas fa-chalkboard-teacher"></i> Data Guru
                        </a>
                        <a class="nav-link" href="/absen/kelas/">
                            <i class="fas fa-school"></i> Data Kelas
                        </a>
                        <a class="nav-link" href="/absen/siswa/">
                            <i class="fas fa-user-graduate"></i> Data Siswa
                        </a>


                        <a class="nav-link" href="/absen/mapel/">
                            <i class="fas fa-book"></i> Data Mapel
                        </a>
                        <a class="nav-link" href="/absen/laporan/">
                            <i class="fas fa-book"></i> Rekap Absensi
                        </a>
                        <a class="nav-link" href="/absen/hari_libur/">
                            <i class="fas fa-calendar-times"></i> Hari Libur
                        </a>
                        <a class="nav-link" href="/absen/tingkat/">
                            <i class="fas fa-layer-group"></i> Data Tingkat
                        </a>
                        <a class="nav-link" href="/absen/jurusan/">
                            <i class="fas fa-graduation-cap"></i> Data Jurusan
                        </a>
                    </div>
                </li>


                <!-- Jadwal Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-calendar"></i> Jadwal
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a class="nav-link" href="/absen/jadwal/">
                            <i class="fas fa-calendar-alt"></i> Jadwal Pelajaran
                        </a>
                        <a class="nav-link" href="/absen/jadwal/config_jam.php">
                            <i class="fas fa-clock"></i> Konfigurasi Jam
                        </a>
                    </div>
                </li>



                <!-- System Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-cogs"></i> Sistem
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a class="nav-link" href="/absen/akun/">
                            <i class="fas fa-users-cog"></i> Manajemen Akun
                        </a>
                        <?php if ($_SESSION['role'] === 'admin'): ?>
                        <a class="nav-link" href="/absen/popup_berita/">
                            <i class="fas fa-bell"></i> Popup Berita
                        </a>
                        <?php endif; ?>
                        <a class="nav-link" href="/absen/maintenance/">
                            <i class="fas fa-tools"></i> Maintenance
                        </a>
                        <a class="nav-link" href="/absen/changelog/">
                            <i class="fas fa-history"></i> Changelog
                        </a>
                    </div>
                </li>

                <!-- Berita Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-newspaper"></i> Berita
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a class="nav-link" href="/absen/berita/">
                            <i class="fas fa-list"></i> Daftar Berita
                        </a>
                        <a class="nav-link" href="/absen/berita/create.php">
                            <i class="fas fa-plus"></i> Tambah Berita
                        </a>
                    </div>
                </li>
                <?php endif; ?>




                <?php if ($_SESSION['role'] === 'admin'): ?>
                <!-- Admin Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-cogs"></i> Admin
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <ul class="nav flex-column nav-group-items">
                        <li class="nav-item">
                            <a class="nav-link" href="/absen/profil_sekolah/">
                                <i class="fas fa-school"></i> Profil Sekolah
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>


                <?php if ($_SESSION['role'] === 'guru'): ?>
                <!-- Guru Activities Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-tasks"></i> Aktivitas Guru
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <?php if($_SESSION['role'] == 'guru'): ?>
                            <a class="nav-link" href="/absen/jadwal/">
                                <i class="fas fa-calendar-alt"></i> Jadwal Mengajar
                            </a>
                            <a class="nav-link" href="/absen/absensi/">
                                <i class="fas fa-clipboard-check"></i> Absensi
                            </a>
                            <a class="nav-link" href="/absen/riwayat_absensi/">
                                <i class="fas fa-history"></i> Riwayat Absensi
                            </a>
                            <a class="nav-link" href="/absen/tugas/">
                                <i class="fas fa-book"></i> Input Tugas
                            </a>
                            <a class="nav-link" href="/absen/nilai/">
                                <i class="fas fa-star"></i> Input Nilai
                            </a>
                            <a class="nav-link" href="/absen/nilai_sikap/index.php">
                                <i class="fas fa-heart"></i> Nilai Sikap
                            </a>
                            <a class="nav-link" href="/absen/tugas_tambahan/">
                                <i class="fas fa-clipboard-list"></i> Tugas Tambahan
                            </a>
                            <a class="nav-link" href="/absen/berita/">
                                <i class="fas fa-newspaper"></i> Berita
                            </a>
                            <a class="nav-link" href="/absen/kd/">
                                <i class="fas fa-graduation-cap"></i> Kompetensi Dasar
                            </a>
                            <a class="nav-link" href="/absen/profil/">
                                <i class="fas fa-user-circle"></i> Profil
                            </a>



                        <?php endif; ?>
                    </div>

                </li>

                <!-- RPP Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-book"></i> RPP
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a class="nav-link" href="/absen/rpp/">
                            <i class="fas fa-list"></i> Daftar RPP
                        </a>
                        <a class="nav-link" href="/absen/rpp/create.php">
                            <i class="fas fa-plus"></i> Tambah RPP
                        </a>
                        <a class="nav-link" href="/absen/rpp/generate_questions.php">
                            <i class="fas fa-magic"></i> Generate Soal
                        </a>
                        <a class="nav-link" href="/absen/rpp/configure_visual_generation.php">
                            <i class="fas fa-chart-line"></i> Generate Soal Visual
                        </a>
                        <a class="nav-link" href="/absen/rpp/visual_examples.php">
                            <i class="fas fa-eye"></i> Contoh Soal Visual
                        </a>
                        <a class="nav-link" href="/absen/rpp/multi_rpp_generate.php">
                            <i class="fas fa-layer-group"></i> Multi-RPP Exam
                        </a>
                        <a class="nav-link" href="/absen/rpp/multi_rpp_list.php">
                            <i class="fas fa-list-alt"></i> Daftar Multi-RPP
                        </a>
                        <a class="nav-link" href="/absen/rpp/blueprint_list.php">
                            <i class="fas fa-file-alt"></i> Kisi-kisi Ujian
                        </a>
                    </div>
                </li>

                <?php endif; ?>

                <?php if ($is_wali_kelas): ?>
                <!-- W Activities Group -->
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-tasks"></i> Wali Kelas
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">

                        <?php if ($is_wali_kelas): ?>
                        <!-- Removed problematic modules: rekap_absensi, rekap_nilai, cetak_rapor -->
                        <?php endif; ?>
                    </div>

                </li>
                <?php endif; ?>

                <!-- Menu Perpustakaan -->
                <?php if ($_SESSION['role'] === 'admin' || $is_pustakawan): ?>
                <li class="nav-item nav-group">
                    <div class="nav-link nav-group-header" onclick="toggleNavGroup(this)">
                        <div>
                            <i class="fas fa-book"></i> Perpustakaan
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="nav-group-items">
                        <a href="/absen/perpustakaan/index.php" class="nav-link">
                            <i class="fas fa-list"></i> Daftar Buku
                        </a>
                        <a href="/absen/perpustakaan/kategori.php" class="nav-link">
                            <i class="fas fa-tags"></i> Kategori
                        </a>
                        <a href="/absen/perpustakaan/peminjaman.php" class="nav-link">
                            <i class="fas fa-book-reader"></i> Peminjaman
                        </a>
                        <a href="/absen/perpustakaan/pengembalian.php" class="nav-link">
                            <i class="fas fa-undo"></i> Pengembalian
                        </a>
                        <a href="/absen/perpustakaan/laporan.php" class="nav-link">
                            <i class="fas fa-chart-bar"></i> Laporan
                        </a>
                        <?php if ($_SESSION['role'] === 'admin'): ?>
                        <a href="/absen/perpustakaan/konfigurasi.php" class="nav-link">
                            <i class="fas fa-cog"></i> Konfigurasi
                        </a>
                        <?php endif; ?>
                    </div>
                
                <?php endif; ?>

                <li class="nav-item">
                    <a class="nav-link" href="/absen/logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div id="content">
        <!-- Topbar -->
        <div class="topbar">
            <div class="menu-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="ms-3">
                <strong>Periode Aktif:</strong>
                Tahun Ajaran <?php echo $active_period->tahun_ajaran; ?> -
                Semester <?php echo $active_period->semester; ?>
            </div>
        </div>

        <!-- Page Content -->
        <div class="container-fluid py-4">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

    <script>
        function toggleNavGroup(element) {
            const navGroup = element.closest('.nav-group');
            navGroup.classList.toggle('collapsed');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('expanded');
            });

            // Initialize nav groups
            const navGroups = document.querySelectorAll('.nav-group');
            navGroups.forEach(group => {
                const header = group.querySelector('.nav-group-header');
                const items = group.querySelector('.nav-group-items');

                // Initially show all nav groups
                items.style.display = 'block';

                header.addEventListener('click', function() {
                    if (items.style.display === 'none' || items.style.display === '') {
                        items.style.display = 'block';
                    } else {
                        items.style.display = 'none';
                    }
                });
            });

            // Set active menu item based on current URL
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.nav-link');
            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                    // Expand parent nav group if exists
                    const parentGroup = item.closest('.nav-group');
                    if (parentGroup) {
                        const items = parentGroup.querySelector('.nav-group-items');
                        items.style.display = 'block';
                    }
                }
            });
        });
    </script>

    <!-- News Popup System -->
    <script src="/absen/assets/js/popup-berita.js"></script>
