<?php
require_once __DIR__ . '/../config/database.php';

class VisualQuestionTemplate {
    private $conn;
    private $table_name = "visual_question_templates";

    public $id;
    public $subject_category;
    public $template_name;
    public $template_description;
    public $html_template;
    public $js_libraries;
    public $css_dependencies;
    public $parameters;
    public $example_data;
    public $is_active;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                subject_category=:subject_category, template_name=:template_name,
                template_description=:template_description, html_template=:html_template,
                js_libraries=:js_libraries, css_dependencies=:css_dependencies,
                parameters=:parameters, example_data=:example_data, is_active=:is_active";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->subject_category = htmlspecialchars(strip_tags($this->subject_category));
        $this->template_name = htmlspecialchars(strip_tags($this->template_name));
        $this->template_description = htmlspecialchars(strip_tags($this->template_description));
        $this->is_active = $this->is_active ?? 1;

        // Handle JSON fields
        if (is_array($this->js_libraries)) {
            $this->js_libraries = json_encode($this->js_libraries);
        }
        if (is_array($this->css_dependencies)) {
            $this->css_dependencies = json_encode($this->css_dependencies);
        }
        if (is_array($this->parameters)) {
            $this->parameters = json_encode($this->parameters);
        }
        if (is_array($this->example_data)) {
            $this->example_data = json_encode($this->example_data);
        }

        $stmt->bindParam(":subject_category", $this->subject_category);
        $stmt->bindParam(":template_name", $this->template_name);
        $stmt->bindParam(":template_description", $this->template_description);
        $stmt->bindParam(":html_template", $this->html_template);
        $stmt->bindParam(":js_libraries", $this->js_libraries);
        $stmt->bindParam(":css_dependencies", $this->css_dependencies);
        $stmt->bindParam(":parameters", $this->parameters);
        $stmt->bindParam(":example_data", $this->example_data);
        $stmt->bindParam(":is_active", $this->is_active);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE is_active = 1 ORDER BY subject_category, template_name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getBySubject($subject_category) {
        $query = "SELECT * FROM " . $this->table_name . " 
                 WHERE subject_category = :subject_category AND is_active = 1 
                 ORDER BY template_name";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":subject_category", $subject_category);
        $stmt->execute();
        return $stmt;
    }

    public function getOne($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                subject_category=:subject_category, template_name=:template_name,
                template_description=:template_description, html_template=:html_template,
                js_libraries=:js_libraries, css_dependencies=:css_dependencies,
                parameters=:parameters, example_data=:example_data, is_active=:is_active
                WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->subject_category = htmlspecialchars(strip_tags($this->subject_category));
        $this->template_name = htmlspecialchars(strip_tags($this->template_name));
        $this->template_description = htmlspecialchars(strip_tags($this->template_description));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Handle JSON fields
        if (is_array($this->js_libraries)) {
            $this->js_libraries = json_encode($this->js_libraries);
        }
        if (is_array($this->css_dependencies)) {
            $this->css_dependencies = json_encode($this->css_dependencies);
        }
        if (is_array($this->parameters)) {
            $this->parameters = json_encode($this->parameters);
        }
        if (is_array($this->example_data)) {
            $this->example_data = json_encode($this->example_data);
        }

        $stmt->bindParam(":subject_category", $this->subject_category);
        $stmt->bindParam(":template_name", $this->template_name);
        $stmt->bindParam(":template_description", $this->template_description);
        $stmt->bindParam(":html_template", $this->html_template);
        $stmt->bindParam(":js_libraries", $this->js_libraries);
        $stmt->bindParam(":css_dependencies", $this->css_dependencies);
        $stmt->bindParam(":parameters", $this->parameters);
        $stmt->bindParam(":example_data", $this->example_data);
        $stmt->bindParam(":is_active", $this->is_active);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        return $stmt->execute();
    }

    public function getSubjectCategories() {
        return [
            'mathematics' => 'Matematika',
            'physics' => 'Fisika',
            'biology' => 'Biologi',
            'chemistry' => 'Kimia',
            'economics' => 'Ekonomi',
            'accounting' => 'Akuntansi',
            'history' => 'Sejarah',
            'geography' => 'Geografi',
            'language' => 'Bahasa',
            'civics' => 'PKn',
            'arts' => 'Seni'
        ];
    }

    public function getVisualTypes() {
        return [
            'graph' => 'Grafik/Chart',
            'table' => 'Tabel Interaktif',
            'map' => 'Peta',
            'animation' => 'Animasi',
            'interactive' => 'Elemen Interaktif',
            'simulation' => 'Simulasi',
            '3d_model' => 'Model 3D',
            'chart' => 'Diagram'
        ];
    }

    public function getRequiredLibraries($subject_category) {
        $libraries = [
            'mathematics' => ['plotly.js', 'mathjax'],
            'physics' => ['plotly.js', 'p5.js', 'mathjax'],
            'biology' => ['plotly.js', 'p5.js'],
            'chemistry' => ['plotly.js', '3dmol.js', 'mathjax'],
            'economics' => ['plotly.js', 'mathjax'],
            'accounting' => ['handsontable', 'plotly.js'],
            'history' => ['timeline.js', 'leaflet.js'],
            'geography' => ['leaflet.js', 'plotly.js'],
            'language' => ['p5.js'],
            'civics' => ['p5.js'],
            'arts' => ['p5.js']
        ];

        return $libraries[$subject_category] ?? [];
    }

    public function initializeDefaultTemplates() {
        // This method will be used to populate default templates
        // Implementation will be added in the next step
        return true;
    }
}
?>
