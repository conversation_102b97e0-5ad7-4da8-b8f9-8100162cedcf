<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contoh Soal Fisika - Gerak Parabola</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .question-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .question-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }
        .simulation-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .animation-panel {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .graph-panel {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }
        .control-group input {
            padding: 8px 12px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 14px;
            width: 80px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn-primary { background-color: #3498db; color: white; }
        .btn-success { background-color: #27ae60; color: white; }
        .btn-warning { background-color: #f39c12; color: white; }
        .btn-danger { background-color: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; }
        .formula-section {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .data-display {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .data-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #bdc3c7;
        }
        .data-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .data-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        @media (max-width: 768px) {
            .simulation-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="question-container">
        <div class="question-title">
            🚀 Soal Fisika: Analisis Gerak Parabola
        </div>
        
        <div class="question-text">
            <strong>Soal:</strong> Sebuah proyektil ditembakkan dari permukaan tanah dengan kecepatan awal v₀ 
            dan sudut elevasi θ. Analisis gerak proyektil tersebut dan tentukan:
            <ol>
                <li>Waktu untuk mencapai titik tertinggi</li>
                <li>Tinggi maksimum yang dicapai</li>
                <li>Jangkauan horizontal maksimum</li>
                <li>Waktu total di udara</li>
            </ol>
        </div>

        <div class="formula-section">
            <h4>📐 Rumus-rumus Gerak Parabola:</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                <div>
                    <strong>Komponen Kecepatan:</strong><br>
                    $$v_{0x} = v_0 \cos \theta$$<br>
                    $$v_{0y} = v_0 \sin \theta$$
                </div>
                <div>
                    <strong>Posisi:</strong><br>
                    $$x = v_{0x} \cdot t$$<br>
                    $$y = v_{0y} \cdot t - \frac{1}{2}gt^2$$
                </div>
                <div>
                    <strong>Tinggi Maksimum:</strong><br>
                    $$h_{max} = \frac{v_{0y}^2}{2g}$$
                </div>
                <div>
                    <strong>Jangkauan:</strong><br>
                    $$R = \frac{v_0^2 \sin 2\theta}{g}$$
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="initial-velocity">Kecepatan Awal (m/s):</label>
                <input type="number" id="initial-velocity" value="50" min="10" max="100" step="5">
            </div>
            <div class="control-group">
                <label for="launch-angle">Sudut Elevasi (°):</label>
                <input type="number" id="launch-angle" value="45" min="15" max="75" step="5">
            </div>
            <div class="control-group">
                <label for="gravity">Gravitasi (m/s²):</label>
                <input type="number" id="gravity" value="9.8" min="9.8" max="9.8" step="0.1" readonly>
            </div>
            <button class="btn btn-success" onclick="startSimulation()">▶️ Mulai</button>
            <button class="btn btn-warning" onclick="pauseSimulation()">⏸️ Pause</button>
            <button class="btn btn-danger" onclick="resetSimulation()">🔄 Reset</button>
            <button class="btn btn-primary" onclick="exportAnimation()">📥 Export</button>
        </div>

        <div class="simulation-container">
            <div class="animation-panel">
                <h5>🎬 Simulasi Gerak</h5>
                <div id="animation-canvas"></div>
            </div>
            <div class="graph-panel">
                <h5>📊 Grafik Kecepatan vs Waktu</h5>
                <div id="velocity-graph" style="width:100%;height:300px;"></div>
            </div>
        </div>

        <div class="data-display">
            <h4>📊 Data Hasil Perhitungan:</h4>
            <div class="data-grid">
                <div class="data-item">
                    <div class="data-value" id="time-to-peak">-</div>
                    <div class="data-label">Waktu ke Puncak (s)</div>
                </div>
                <div class="data-item">
                    <div class="data-value" id="max-height">-</div>
                    <div class="data-label">Tinggi Maksimum (m)</div>
                </div>
                <div class="data-item">
                    <div class="data-value" id="range">-</div>
                    <div class="data-label">Jangkauan (m)</div>
                </div>
                <div class="data-item">
                    <div class="data-value" id="total-time">-</div>
                    <div class="data-label">Waktu Total (s)</div>
                </div>
                <div class="data-item">
                    <div class="data-value" id="current-height">-</div>
                    <div class="data-label">Ketinggian Saat Ini (m)</div>
                </div>
                <div class="data-item">
                    <div class="data-value" id="current-velocity">-</div>
                    <div class="data-label">Kecepatan Saat Ini (m/s)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // MathJax configuration
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };

        let sketch;
        let isAnimating = false;
        let projectile = {};
        let trail = [];
        let timeData = [];
        let velocityData = [];
        let currentTime = 0;

        function initializeSimulation() {
            const v0 = parseFloat(document.getElementById('initial-velocity').value);
            const angle = parseFloat(document.getElementById('launch-angle').value);
            const g = parseFloat(document.getElementById('gravity').value);
            
            const angleRad = angle * Math.PI / 180;
            const v0x = v0 * Math.cos(angleRad);
            const v0y = v0 * Math.sin(angleRad);
            
            // Calculate theoretical values
            const timeToPeak = v0y / g;
            const maxHeight = (v0y * v0y) / (2 * g);
            const totalTime = 2 * timeToPeak;
            const range = (v0 * v0 * Math.sin(2 * angleRad)) / g;
            
            // Update display
            document.getElementById('time-to-peak').textContent = timeToPeak.toFixed(2);
            document.getElementById('max-height').textContent = maxHeight.toFixed(2);
            document.getElementById('range').textContent = range.toFixed(2);
            document.getElementById('total-time').textContent = totalTime.toFixed(2);
            
            // Initialize projectile
            projectile = {
                x: 50,
                y: 350,
                vx: v0x * 0.8, // Scale for visualization
                vy: -v0y * 0.8, // Negative because y increases downward
                v0x: v0x,
                v0y: v0y,
                g: g,
                maxHeight: maxHeight,
                range: range,
                totalTime: totalTime
            };
            
            trail = [];
            timeData = [];
            velocityData = [];
            currentTime = 0;
        }

        function createSketch() {
            sketch = new p5(function(p) {
                p.setup = function() {
                    p.createCanvas(500, 400).parent('animation-canvas');
                    initializeSimulation();
                };
                
                p.draw = function() {
                    p.background(135, 206, 235); // Sky blue
                    
                    // Draw ground
                    p.fill(34, 139, 34);
                    p.rect(0, 350, 500, 50);
                    
                    // Draw trajectory trail
                    p.stroke(255, 0, 0);
                    p.strokeWeight(2);
                    p.noFill();
                    p.beginShape();
                    for (let point of trail) {
                        p.vertex(point.x, point.y);
                    }
                    p.endShape();
                    
                    // Draw projectile
                    p.fill(255, 0, 0);
                    p.noStroke();
                    p.ellipse(projectile.x, projectile.y, 12, 12);
                    
                    // Draw velocity vector
                    p.stroke(0, 255, 0);
                    p.strokeWeight(3);
                    const vScale = 0.5;
                    p.line(projectile.x, projectile.y, 
                          projectile.x + projectile.vx * vScale, 
                          projectile.y + projectile.vy * vScale);
                    
                    // Draw info
                    p.fill(0);
                    p.noStroke();
                    p.textSize(12);
                    p.text(`Waktu: ${currentTime.toFixed(2)}s`, 10, 20);
                    p.text(`Posisi: (${((projectile.x - 50) * 0.5).toFixed(1)}, ${((350 - projectile.y) * 0.5).toFixed(1)})m`, 10, 35);
                    
                    if (isAnimating && projectile.y < 350) {
                        // Update physics
                        const dt = 0.05;
                        currentTime += dt;
                        
                        trail.push({x: projectile.x, y: projectile.y});
                        if (trail.length > 100) trail.shift();
                        
                        projectile.x += projectile.vx * dt;
                        projectile.y += projectile.vy * dt;
                        projectile.vy += projectile.g * dt * 0.8; // Scale gravity
                        
                        // Update real-time data
                        const realHeight = (350 - projectile.y) * 0.5;
                        const realVelocity = Math.sqrt(projectile.vx * projectile.vx + projectile.vy * projectile.vy) / 0.8;
                        
                        document.getElementById('current-height').textContent = Math.max(0, realHeight).toFixed(2);
                        document.getElementById('current-velocity').textContent = realVelocity.toFixed(2);
                        
                        // Store data for graph
                        timeData.push(currentTime);
                        velocityData.push(realVelocity);
                        
                        updateVelocityGraph();
                        
                        // Stop when hits ground
                        if (projectile.y >= 350) {
                            isAnimating = false;
                            projectile.y = 350;
                        }
                    }
                };
            });
        }

        function updateVelocityGraph() {
            if (timeData.length < 2) return;
            
            const trace = {
                x: timeData,
                y: velocityData,
                type: 'scatter',
                mode: 'lines',
                name: 'Kecepatan Total',
                line: { color: '#e74c3c', width: 3 }
            };
            
            const layout = {
                title: 'Kecepatan vs Waktu',
                xaxis: { title: 'Waktu (s)' },
                yaxis: { title: 'Kecepatan (m/s)' },
                showlegend: false,
                margin: { l: 50, r: 20, t: 40, b: 40 }
            };
            
            Plotly.newPlot('velocity-graph', [trace], layout, {responsive: true});
        }

        function startSimulation() {
            if (!isAnimating) {
                initializeSimulation();
                isAnimating = true;
            }
        }

        function pauseSimulation() {
            isAnimating = false;
        }

        function resetSimulation() {
            isAnimating = false;
            initializeSimulation();
            document.getElementById('current-height').textContent = '-';
            document.getElementById('current-velocity').textContent = '-';
            Plotly.purge('velocity-graph');
        }

        function exportAnimation() {
            // Export current frame as image
            if (sketch) {
                sketch.save('gerak-parabola-simulasi.png');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            createSketch();
            
            // Add event listeners for parameter changes
            ['initial-velocity', 'launch-angle'].forEach(id => {
                document.getElementById(id).addEventListener('change', function() {
                    if (!isAnimating) {
                        initializeSimulation();
                    }
                });
            });
        });

        // Listen for export messages from parent
        window.addEventListener('message', function(event) {
            if (event.data.action === 'export') {
                exportAnimation();
            }
        });
    </script>
</body>
</html>
