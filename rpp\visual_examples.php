<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye"></i> Contoh Soal Visual Interaktif
                    </h5>
                    <p class="text-muted mb-0">Lihat contoh soal dengan visualisasi untuk berbagai mata pelajaran</p>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="subjectTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="math-tab" data-bs-toggle="tab" data-bs-target="#math" type="button" role="tab">Matematika</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="physics-tab" data-bs-toggle="tab" data-bs-target="#physics" type="button" role="tab">Fisika</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="biology-tab" data-bs-toggle="tab" data-bs-target="#biology" type="button" role="tab">Biologi</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chemistry-tab" data-bs-toggle="tab" data-bs-target="#chemistry" type="button" role="tab">Kimia</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="economics-tab" data-bs-toggle="tab" data-bs-target="#economics" type="button" role="tab">Ekonomi</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="accounting-tab" data-bs-toggle="tab" data-bs-target="#accounting" type="button" role="tab">Akuntansi</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="geography-tab" data-bs-toggle="tab" data-bs-target="#geography" type="button" role="tab">Geografi</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="language-tab" data-bs-toggle="tab" data-bs-target="#language" type="button" role="tab">Bahasa</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="subjectTabContent">
                        <!-- Mathematics Example -->
                        <div class="tab-pane fade show active" id="math" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Grafik Fungsi Kuadrat</h6>
                                <p><strong>Soal:</strong> Perhatikan grafik fungsi f(x) = ax² + bx + c di bawah ini. Tentukan nilai a, b, dan c berdasarkan grafik yang ditampilkan!</p>
                                
                                <div id="mathExample" style="width:100%;height:400px;"></div>
                                <button class="btn btn-sm btn-primary mt-2" onclick="exportMathChart()">Export Grafik</button>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Grafik interaktif dengan Plotly.js</li>
                                        <li>Zoom dan pan untuk eksplorasi detail</li>
                                        <li>Rumus matematika dengan MathJax</li>
                                        <li>Export ke PNG/JPG</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Physics Example -->
                        <div class="tab-pane fade" id="physics" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Simulasi Gerak Parabola</h6>
                                <p><strong>Soal:</strong> Sebuah proyektil ditembakkan dengan kecepatan awal 50 m/s pada sudut 45°. Analisis gerak proyektil tersebut!</p>
                                
                                <div id="physicsExample" style="width:100%;height:400px;border:1px solid #ccc;"></div>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-success" onclick="startPhysicsAnimation()">Mulai Simulasi</button>
                                    <button class="btn btn-sm btn-warning" onclick="resetPhysicsAnimation()">Reset</button>
                                    <button class="btn btn-sm btn-primary" onclick="exportPhysicsChart()">Export</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Animasi gerak dengan p5.js</li>
                                        <li>Kontrol interaktif (play/pause/reset)</li>
                                        <li>Grafik kecepatan dan posisi real-time</li>
                                        <li>Rumus fisika dengan MathJax</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Biology Example -->
                        <div class="tab-pane fade" id="biology" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Pertumbuhan Populasi</h6>
                                <p><strong>Soal:</strong> Grafik berikut menunjukkan pertumbuhan populasi bakteri dalam medium kultur. Analisis fase-fase pertumbuhan!</p>
                                
                                <div id="biologyExample" style="width:100%;height:400px;"></div>
                                <button class="btn btn-sm btn-primary mt-2" onclick="exportBiologyChart()">Export Grafik</button>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Grafik pertumbuhan eksponensial</li>
                                        <li>Animasi fase pertumbuhan</li>
                                        <li>Interaktif hover untuk detail data</li>
                                        <li>Multiple dataset comparison</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Chemistry Example -->
                        <div class="tab-pane fade" id="chemistry" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Struktur Molekul 3D</h6>
                                <p><strong>Soal:</strong> Perhatikan struktur molekul kafein (C₈H₁₀N₄O₂) berikut. Identifikasi jenis ikatan dan geometri molekul!</p>
                                
                                <div id="chemistryExample" style="width:100%;height:400px;border:1px solid #ccc;"></div>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-info" onclick="rotateMolecule()">Rotasi Otomatis</button>
                                    <button class="btn btn-sm btn-primary" onclick="exportChemistryModel()">Export Model</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Model molekul 3D dengan 3Dmol.js</li>
                                        <li>Rotasi dan zoom interaktif</li>
                                        <li>Berbagai style representasi (ball-stick, space-filling)</li>
                                        <li>Informasi atom dan ikatan on-hover</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Economics Example -->
                        <div class="tab-pane fade" id="economics" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Kurva Supply dan Demand</h6>
                                <p><strong>Soal:</strong> Berdasarkan kurva supply dan demand berikut, tentukan titik keseimbangan pasar dan analisis dampak perubahan harga!</p>
                                
                                <div id="economicsExample" style="width:100%;height:400px;"></div>
                                <div class="mt-2">
                                    <label>Geser untuk mengubah supply: </label>
                                    <input type="range" id="supplySlider" min="-2" max="2" step="0.1" value="0" onchange="updateSupplyDemand()">
                                    <button class="btn btn-sm btn-primary ms-2" onclick="exportEconomicsChart()">Export</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Kurva interaktif dengan slider kontrol</li>
                                        <li>Real-time update titik keseimbangan</li>
                                        <li>Shaded area untuk surplus konsumen/produsen</li>
                                        <li>Rumus ekonomi dengan MathJax</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Accounting Example -->
                        <div class="tab-pane fade" id="accounting" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Neraca Interaktif</h6>
                                <p><strong>Soal:</strong> Lengkapi neraca berikut dan pastikan keseimbangan Aktiva = Pasiva!</p>
                                
                                <div id="accountingExample" style="width:100%;height:400px;"></div>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-success" onclick="checkBalance()">Cek Keseimbangan</button>
                                    <button class="btn btn-sm btn-primary" onclick="exportAccountingTable()">Export Tabel</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Tabel interaktif dengan Handsontable</li>
                                        <li>Auto-calculation dan validasi</li>
                                        <li>Highlight error dan warning</li>
                                        <li>Export ke Excel/CSV</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Geography Example -->
                        <div class="tab-pane fade" id="geography" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Peta Interaktif Indonesia</h6>
                                <p><strong>Soal:</strong> Identifikasi provinsi-provinsi di Indonesia dan analisis data demografinya berdasarkan peta tematik berikut!</p>
                                
                                <div id="geographyExample" style="width:100%;height:400px;"></div>
                                <div class="mt-2">
                                    <select id="mapLayer" onchange="changeMapLayer()">
                                        <option value="population">Kepadatan Penduduk</option>
                                        <option value="gdp">GDP per Kapita</option>
                                        <option value="education">Tingkat Pendidikan</option>
                                    </select>
                                    <button class="btn btn-sm btn-primary ms-2" onclick="exportGeographyMap()">Export Peta</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Peta interaktif dengan Leaflet.js</li>
                                        <li>Multiple layer data tematik</li>
                                        <li>Popup informasi detail per provinsi</li>
                                        <li>Zoom dan pan navigation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Language Example -->
                        <div class="tab-pane fade" id="language" role="tabpanel">
                            <div class="mt-4">
                                <h6 class="fw-bold">Contoh: Kuis Interaktif Tata Bahasa</h6>
                                <p><strong>Soal:</strong> Seret kata-kata berikut ke posisi yang tepat untuk membentuk kalimat yang benar!</p>
                                
                                <div id="languageExample" style="width:100%;height:400px;border:1px solid #ccc;padding:20px;"></div>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-warning" onclick="resetLanguageQuiz()">Reset</button>
                                    <button class="btn btn-sm btn-success" onclick="checkLanguageAnswer()">Cek Jawaban</button>
                                    <button class="btn btn-sm btn-primary" onclick="exportLanguageQuiz()">Export</button>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Fitur Visual:</strong>
                                    <ul>
                                        <li>Drag and drop interface</li>
                                        <li>Real-time feedback</li>
                                        <li>Animasi dan sound effects</li>
                                        <li>Progressive difficulty levels</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <a href="configure_visual_generation.php" class="btn btn-primary">
                                    <i class="fas fa-magic"></i> Mulai Generate Soal Visual
                                </a>
                                <a href="generate_questions.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Generate Biasa
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include required libraries -->
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.0.4/3Dmol-min.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">

<script>
// Mathematics Example - Function Graph
function initMathExample() {
    const x = [];
    const y = [];
    for (let i = -10; i <= 10; i += 0.1) {
        x.push(i);
        y.push(i * i - 4 * i + 3); // f(x) = x² - 4x + 3
    }
    
    const trace = {
        x: x,
        y: y,
        type: 'scatter',
        mode: 'lines',
        name: 'f(x) = x² - 4x + 3',
        line: { color: 'blue', width: 3 }
    };
    
    const layout = {
        title: 'Grafik Fungsi Kuadrat f(x) = x² - 4x + 3',
        xaxis: { title: 'x', zeroline: true, gridcolor: '#ddd' },
        yaxis: { title: 'f(x)', zeroline: true, gridcolor: '#ddd' },
        showlegend: true,
        hovermode: 'closest'
    };
    
    Plotly.newPlot('mathExample', [trace], layout, {responsive: true});
}

function exportMathChart() {
    Plotly.toImage('mathExample', {format: 'png', width: 800, height: 400})
        .then(function(url) {
            const link = document.createElement('a');
            link.download = 'grafik-fungsi-kuadrat.png';
            link.href = url;
            link.click();
        });
}

// Physics Example - Projectile Motion
let physicsSketch;
function initPhysicsExample() {
    physicsSketch = new p5(function(p) {
        let projectile = { x: 50, y: 350, vx: 8, vy: -12, trail: [] };
        let isAnimating = false;
        
        p.setup = function() {
            p.createCanvas(800, 400).parent('physicsExample');
        };
        
        p.draw = function() {
            p.background(220);
            
            // Draw ground
            p.fill(100, 200, 100);
            p.rect(0, 350, 800, 50);
            
            // Draw trajectory trail
            p.stroke(255, 0, 0);
            p.strokeWeight(2);
            for (let i = 1; i < projectile.trail.length; i++) {
                p.line(projectile.trail[i-1].x, projectile.trail[i-1].y, 
                      projectile.trail[i].x, projectile.trail[i].y);
            }
            
            // Draw projectile
            p.fill(255, 0, 0);
            p.noStroke();
            p.ellipse(projectile.x, projectile.y, 10, 10);
            
            if (isAnimating) {
                // Update physics
                projectile.trail.push({x: projectile.x, y: projectile.y});
                projectile.x += projectile.vx;
                projectile.y += projectile.vy;
                projectile.vy += 0.5; // gravity
                
                // Reset if hits ground
                if (projectile.y >= 350) {
                    isAnimating = false;
                }
            }
        };
        
        window.startPhysicsAnimation = function() {
            isAnimating = true;
        };
        
        window.resetPhysicsAnimation = function() {
            projectile = { x: 50, y: 350, vx: 8, vy: -12, trail: [] };
            isAnimating = false;
        };
    });
}

function exportPhysicsChart() {
    // Implementation for exporting physics animation
    alert('Export physics simulation - Implementation needed');
}

// Initialize examples when tabs are shown
document.addEventListener('DOMContentLoaded', function() {
    initMathExample();
    initPhysicsExample();
    
    // Initialize other examples when their tabs are clicked
    document.getElementById('biology-tab').addEventListener('click', initBiologyExample);
    document.getElementById('chemistry-tab').addEventListener('click', initChemistryExample);
    document.getElementById('economics-tab').addEventListener('click', initEconomicsExample);
    document.getElementById('accounting-tab').addEventListener('click', initAccountingExample);
    document.getElementById('geography-tab').addEventListener('click', initGeographyExample);
    document.getElementById('language-tab').addEventListener('click', initLanguageExample);
});

// Placeholder functions for other examples
function initBiologyExample() {
    // Biology population growth chart implementation
    console.log('Biology example initialized');
}

function initChemistryExample() {
    // 3D molecule visualization implementation
    console.log('Chemistry example initialized');
}

function initEconomicsExample() {
    // Supply and demand curves implementation
    console.log('Economics example initialized');
}

function initAccountingExample() {
    // Interactive balance sheet implementation
    console.log('Accounting example initialized');
}

function initGeographyExample() {
    // Interactive map implementation
    console.log('Geography example initialized');
}

function initLanguageExample() {
    // Drag and drop language quiz implementation
    console.log('Language example initialized');
}

// Export functions (placeholders)
function exportBiologyChart() { alert('Export biology chart - Implementation needed'); }
function exportChemistryModel() { alert('Export chemistry model - Implementation needed'); }
function exportEconomicsChart() { alert('Export economics chart - Implementation needed'); }
function exportAccountingTable() { alert('Export accounting table - Implementation needed'); }
function exportGeographyMap() { alert('Export geography map - Implementation needed'); }
function exportLanguageQuiz() { alert('Export language quiz - Implementation needed'); }
</script>

<?php require_once '../template/footer.php'; ?>
