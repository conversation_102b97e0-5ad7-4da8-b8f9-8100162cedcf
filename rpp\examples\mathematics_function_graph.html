<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contoh Soal Matematika - Grafik <PERSON>i</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .question-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .question-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .question-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 25px;
            color: #34495e;
        }
        .graph-container {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }
        .control-group input {
            padding: 8px 12px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 14px;
            width: 80px;
        }
        .control-group input:focus {
            outline: none;
            border-color: #3498db;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .formula-display {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-size: 18px;
            border-left: 4px solid #3498db;
        }
        .answer-section {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin-top: 25px;
        }
        .answer-title {
            color: #27ae60;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .option {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .option:hover {
            background: #e9ecef;
            border-color: #3498db;
        }
        .option.correct {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }
        .export-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="question-container">
        <div class="question-title">
            📊 Soal Matematika: Analisis Grafik Fungsi Kuadrat
        </div>
        
        <div class="question-text">
            <strong>Soal:</strong> Perhatikan grafik fungsi kuadrat f(x) = ax² + bx + c di bawah ini. 
            Berdasarkan grafik yang ditampilkan, tentukan nilai koefisien a, b, dan c, 
            serta analisis karakteristik fungsi tersebut!
        </div>

        <div class="formula-display">
            $$f(x) = ax^2 + bx + c$$
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="coeff-a">Koefisien a:</label>
                <input type="number" id="coeff-a" value="1" step="0.1" min="-5" max="5">
            </div>
            <div class="control-group">
                <label for="coeff-b">Koefisien b:</label>
                <input type="number" id="coeff-b" value="-4" step="0.1" min="-10" max="10">
            </div>
            <div class="control-group">
                <label for="coeff-c">Koefisien c:</label>
                <input type="number" id="coeff-c" value="3" step="0.1" min="-10" max="10">
            </div>
            <button class="btn btn-primary" onclick="updateGraph()">Update Grafik</button>
            <button class="btn btn-success" onclick="showAnswer()">Lihat Jawaban</button>
        </div>

        <div class="graph-container">
            <div id="function-graph" style="width:100%;height:500px;"></div>
        </div>

        <div class="answer-section" id="answer-section" style="display: none;">
            <div class="answer-title">💡 Analisis Jawaban:</div>
            <div id="analysis-content"></div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    A. a = 1, b = -2, c = 1
                </div>
                <div class="option correct" onclick="selectOption(this, true)">
                    B. a = 1, b = -4, c = 3
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    C. a = -1, b = 4, c = -3
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    D. a = 2, b = -4, c = 2
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    E. a = 1, b = -6, c = 5
                </div>
            </div>
        </div>

        <div class="export-section">
            <button class="btn btn-primary" onclick="exportGraph()">
                📥 Export Grafik sebagai PNG
            </button>
            <button class="btn btn-success" onclick="exportFullQuestion()">
                📄 Export Soal Lengkap
            </button>
        </div>
    </div>

    <script>
        // MathJax configuration
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };

        let currentA = 1, currentB = -4, currentC = 3;

        function generateFunctionData(a, b, c) {
            const x = [];
            const y = [];
            
            for (let i = -8; i <= 8; i += 0.1) {
                x.push(i);
                y.push(a * i * i + b * i + c);
            }
            
            return { x, y };
        }

        function calculateVertex(a, b, c) {
            const h = -b / (2 * a);
            const k = a * h * h + b * h + c;
            return { h, k };
        }

        function calculateRoots(a, b, c) {
            const discriminant = b * b - 4 * a * c;
            if (discriminant < 0) return null;
            
            const root1 = (-b + Math.sqrt(discriminant)) / (2 * a);
            const root2 = (-b - Math.sqrt(discriminant)) / (2 * a);
            return { root1, root2, discriminant };
        }

        function initializeGraph() {
            updateGraph();
        }

        function updateGraph() {
            const a = parseFloat(document.getElementById('coeff-a').value);
            const b = parseFloat(document.getElementById('coeff-b').value);
            const c = parseFloat(document.getElementById('coeff-c').value);
            
            currentA = a;
            currentB = b;
            currentC = c;

            const data = generateFunctionData(a, b, c);
            const vertex = calculateVertex(a, b, c);
            const roots = calculateRoots(a, b, c);

            // Main function trace
            const functionTrace = {
                x: data.x,
                y: data.y,
                type: 'scatter',
                mode: 'lines',
                name: `f(x) = ${a}x² + ${b}x + ${c}`,
                line: { 
                    color: '#3498db', 
                    width: 4 
                },
                hovertemplate: 'x: %{x:.2f}<br>f(x): %{y:.2f}<extra></extra>'
            };

            // Vertex point
            const vertexTrace = {
                x: [vertex.h],
                y: [vertex.k],
                type: 'scatter',
                mode: 'markers',
                name: `Vertex (${vertex.h.toFixed(2)}, ${vertex.k.toFixed(2)})`,
                marker: { 
                    color: '#e74c3c', 
                    size: 12,
                    symbol: 'diamond'
                },
                hovertemplate: 'Vertex<br>x: %{x:.2f}<br>y: %{y:.2f}<extra></extra>'
            };

            const traces = [functionTrace, vertexTrace];

            // Add roots if they exist
            if (roots && roots.discriminant >= 0) {
                const rootsTrace = {
                    x: [roots.root1, roots.root2],
                    y: [0, 0],
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Akar-akar',
                    marker: { 
                        color: '#27ae60', 
                        size: 10,
                        symbol: 'circle'
                    },
                    hovertemplate: 'Akar: %{x:.2f}<extra></extra>'
                };
                traces.push(rootsTrace);
            }

            const layout = {
                title: {
                    text: `Grafik Fungsi f(x) = ${a}x² + ${b}x + ${c}`,
                    font: { size: 20, color: '#2c3e50' }
                },
                xaxis: { 
                    title: 'x',
                    zeroline: true,
                    gridcolor: '#ecf0f1',
                    gridwidth: 1,
                    range: [-8, 8]
                },
                yaxis: { 
                    title: 'f(x)',
                    zeroline: true,
                    gridcolor: '#ecf0f1',
                    gridwidth: 1
                },
                showlegend: true,
                legend: {
                    x: 0.02,
                    y: 0.98,
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                },
                hovermode: 'closest',
                plot_bgcolor: '#ffffff',
                paper_bgcolor: '#ffffff'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                displaylogo: false,
                toImageButtonOptions: {
                    format: 'png',
                    filename: 'grafik-fungsi-kuadrat',
                    height: 500,
                    width: 800,
                    scale: 2
                }
            };

            Plotly.newPlot('function-graph', traces, layout, config);
        }

        function showAnswer() {
            const answerSection = document.getElementById('answer-section');
            const analysisContent = document.getElementById('analysis-content');
            
            const vertex = calculateVertex(currentA, currentB, currentC);
            const roots = calculateRoots(currentA, currentB, currentC);
            
            let analysis = `
                <p><strong>Analisis Fungsi f(x) = ${currentA}x² + ${currentB}x + ${currentC}:</strong></p>
                <ul>
                    <li><strong>Koefisien a = ${currentA}</strong> → Parabola ${currentA > 0 ? 'membuka ke atas' : 'membuka ke bawah'}</li>
                    <li><strong>Vertex:</strong> (${vertex.h.toFixed(2)}, ${vertex.k.toFixed(2)})</li>
                    <li><strong>Sumbu simetri:</strong> x = ${vertex.h.toFixed(2)}</li>
                    <li><strong>Nilai ${currentA > 0 ? 'minimum' : 'maksimum'}:</strong> f(${vertex.h.toFixed(2)}) = ${vertex.k.toFixed(2)}</li>
            `;
            
            if (roots) {
                if (roots.discriminant > 0) {
                    analysis += `
                        <li><strong>Akar-akar:</strong> x₁ = ${roots.root1.toFixed(2)}, x₂ = ${roots.root2.toFixed(2)}</li>
                        <li><strong>Diskriminan:</strong> ${roots.discriminant.toFixed(2)} > 0 (dua akar real berbeda)</li>
                    `;
                } else if (roots.discriminant === 0) {
                    analysis += `
                        <li><strong>Akar kembar:</strong> x = ${roots.root1.toFixed(2)}</li>
                        <li><strong>Diskriminan:</strong> 0 (satu akar real)</li>
                    `;
                }
            } else {
                analysis += `<li><strong>Tidak memiliki akar real</strong> (diskriminan < 0)</li>`;
            }
            
            analysis += `
                    <li><strong>Titik potong sumbu y:</strong> (0, ${currentC})</li>
                </ul>
            `;
            
            analysisContent.innerHTML = analysis;
            answerSection.style.display = 'block';
            
            // Re-render MathJax
            if (window.MathJax) {
                MathJax.typesetPromise([analysisContent]);
            }
        }

        function selectOption(element, isCorrect) {
            // Remove previous selections
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.backgroundColor = '';
                opt.style.borderColor = '#dee2e6';
                opt.style.color = '';
            });
            
            if (isCorrect) {
                element.style.backgroundColor = '#d4edda';
                element.style.borderColor = '#27ae60';
                element.style.color = '#155724';
                alert('🎉 Benar! Jawaban Anda tepat.');
            } else {
                element.style.backgroundColor = '#f8d7da';
                element.style.borderColor = '#dc3545';
                element.style.color = '#721c24';
                alert('❌ Kurang tepat. Coba analisis grafik lebih teliti.');
            }
        }

        function exportGraph() {
            Plotly.toImage('function-graph', {
                format: 'png',
                width: 800,
                height: 500,
                scale: 2
            }).then(function(url) {
                const link = document.createElement('a');
                link.download = `grafik-fungsi-${currentA}x2${currentB >= 0 ? '+' : ''}${currentB}x${currentC >= 0 ? '+' : ''}${currentC}.png`;
                link.href = url;
                link.click();
            });
        }

        function exportFullQuestion() {
            // This would export the entire question including graph
            alert('Fitur export soal lengkap akan segera tersedia!');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeGraph();
            
            // Add event listeners for real-time updates
            ['coeff-a', 'coeff-b', 'coeff-c'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateGraph);
            });
        });

        // Listen for export messages from parent
        window.addEventListener('message', function(event) {
            if (event.data.action === 'export') {
                exportGraph();
            }
        });
    </script>
</body>
</html>
