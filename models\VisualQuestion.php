<?php
require_once __DIR__ . '/../config/database.php';

class VisualQuestion {
    private $conn;
    private $table_name = "visual_questions";

    public $id;
    public $question_id;
    public $question_type;
    public $template_id;
    public $visual_type;
    public $visual_config;
    public $html_content;
    public $export_image_data;
    public $is_exportable;
    public $generation_metadata;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                question_id=:question_id, question_type=:question_type,
                template_id=:template_id, visual_type=:visual_type,
                visual_config=:visual_config, html_content=:html_content,
                export_image_data=:export_image_data, is_exportable=:is_exportable,
                generation_metadata=:generation_metadata";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->question_id = htmlspecialchars(strip_tags($this->question_id));
        $this->question_type = htmlspecialchars(strip_tags($this->question_type));
        $this->visual_type = htmlspecialchars(strip_tags($this->visual_type));
        $this->is_exportable = $this->is_exportable ?? 1;

        // Handle JSON fields
        if (is_array($this->visual_config)) {
            $this->visual_config = json_encode($this->visual_config);
        }
        if (is_array($this->generation_metadata)) {
            $this->generation_metadata = json_encode($this->generation_metadata);
        }

        $stmt->bindParam(":question_id", $this->question_id);
        $stmt->bindParam(":question_type", $this->question_type);
        $stmt->bindParam(":template_id", $this->template_id);
        $stmt->bindParam(":visual_type", $this->visual_type);
        $stmt->bindParam(":visual_config", $this->visual_config);
        $stmt->bindParam(":html_content", $this->html_content);
        $stmt->bindParam(":export_image_data", $this->export_image_data);
        $stmt->bindParam(":is_exportable", $this->is_exportable);
        $stmt->bindParam(":generation_metadata", $this->generation_metadata);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getByQuestionId($question_id, $question_type) {
        $query = "SELECT vq.*, vqt.template_name, vqt.subject_category 
                 FROM " . $this->table_name . " vq
                 LEFT JOIN visual_question_templates vqt ON vq.template_id = vqt.id
                 WHERE vq.question_id = :question_id AND vq.question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function hasVisualContent($question_id, $question_type) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                 WHERE question_id = :question_id AND question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                template_id=:template_id, visual_type=:visual_type,
                visual_config=:visual_config, html_content=:html_content,
                export_image_data=:export_image_data, is_exportable=:is_exportable,
                generation_metadata=:generation_metadata
                WHERE question_id=:question_id AND question_type=:question_type";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->question_id = htmlspecialchars(strip_tags($this->question_id));
        $this->question_type = htmlspecialchars(strip_tags($this->question_type));
        $this->visual_type = htmlspecialchars(strip_tags($this->visual_type));

        // Handle JSON fields
        if (is_array($this->visual_config)) {
            $this->visual_config = json_encode($this->visual_config);
        }
        if (is_array($this->generation_metadata)) {
            $this->generation_metadata = json_encode($this->generation_metadata);
        }

        $stmt->bindParam(":question_id", $this->question_id);
        $stmt->bindParam(":question_type", $this->question_type);
        $stmt->bindParam(":template_id", $this->template_id);
        $stmt->bindParam(":visual_type", $this->visual_type);
        $stmt->bindParam(":visual_config", $this->visual_config);
        $stmt->bindParam(":html_content", $this->html_content);
        $stmt->bindParam(":export_image_data", $this->export_image_data);
        $stmt->bindParam(":is_exportable", $this->is_exportable);
        $stmt->bindParam(":generation_metadata", $this->generation_metadata);

        return $stmt->execute();
    }

    public function delete($question_id, $question_type) {
        $query = "DELETE FROM " . $this->table_name . " 
                 WHERE question_id = :question_id AND question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        return $stmt->execute();
    }

    public function getVisualQuestionsByRpp($rpp_id) {
        $query = "SELECT vq.*, rq.question_text, rq.question_type as base_question_type,
                         vqt.template_name, vqt.subject_category
                 FROM " . $this->table_name . " vq
                 JOIN rpp_questions rq ON vq.question_id = rq.id
                 LEFT JOIN visual_question_templates vqt ON vq.template_id = vqt.id
                 WHERE rq.rpp_id = :rpp_id AND vq.question_type = 'rpp_question'
                 ORDER BY rq.id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        $stmt->execute();
        return $stmt;
    }

    public function getVisualQuestionsByMultiRpp($exam_id) {
        $query = "SELECT vq.*, mrq.question_text, mrq.question_type as base_question_type,
                         vqt.template_name, vqt.subject_category
                 FROM " . $this->table_name . " vq
                 JOIN multi_rpp_questions mrq ON vq.question_id = mrq.id
                 LEFT JOIN visual_question_templates vqt ON vq.template_id = vqt.id
                 WHERE mrq.multi_exam_id = :exam_id AND vq.question_type = 'multi_rpp_question'
                 ORDER BY mrq.id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":exam_id", $exam_id);
        $stmt->execute();
        return $stmt;
    }

    public function generateExportImage($visual_config, $html_content) {
        // This method will generate static images for export
        // Implementation will use headless browser or server-side rendering
        // For now, return placeholder
        return base64_encode("placeholder_image_data");
    }

    public function getVisualTypes() {
        return [
            'graph' => 'Grafik/Chart',
            'table' => 'Tabel Interaktif',
            'map' => 'Peta',
            'animation' => 'Animasi',
            'interactive' => 'Elemen Interaktif',
            'simulation' => 'Simulasi',
            '3d_model' => 'Model 3D',
            'chart' => 'Diagram'
        ];
    }

    public function getStatistics() {
        $query = "SELECT 
                    visual_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN is_exportable = 1 THEN 1 END) as exportable_count
                 FROM " . $this->table_name . "
                 GROUP BY visual_type
                 ORDER BY count DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function bulkDelete($question_ids, $question_type) {
        if (empty($question_ids)) {
            return false;
        }

        $placeholders = str_repeat('?,', count($question_ids) - 1) . '?';
        $query = "DELETE FROM " . $this->table_name . " 
                 WHERE question_id IN ($placeholders) AND question_type = ?";
        
        $stmt = $this->conn->prepare($query);
        $params = array_merge($question_ids, [$question_type]);
        return $stmt->execute($params);
    }
}
?>
