<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../models/Rpp.php';
require_once '../models/Guru.php';
require_once '../models/GeminiApi.php';
require_once '../models/RppQuestion.php';
require_once '../models/VisualQuestion.php';
require_once '../models/VisualQuestionTemplate.php';

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Invalid request method.";
    header("Location: configure_visual_generation.php");
    exit();
}

// Validate and get form data
$rpp_id = (int)$_POST['rpp_id'];
$config = [
    'subject_category' => $_POST['subject_category'] ?? '',
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'visual_question_count' => (int)$_POST['visual_question_count'],
    'visual_types' => $_POST['visual_types'] ?? [],
    'visual_complexity' => $_POST['visual_complexity'] ?? 'moderate',
    'enable_visuals' => isset($_POST['enable_visuals']),
    'include_visuals_in_export' => isset($_POST['include_visuals_in_export']),
    'export_image_quality' => $_POST['export_image_quality'] ?? 'medium',
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count']
];

// Validate RPP ownership
$rpp = new Rpp();
$rpp_data = $rpp->getOne($rpp_id);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: configure_visual_generation.php");
    exit();
}

// Validate configuration
$total_by_type = $config['multiple_choice_count'] + $config['essay_count'];
$total_by_difficulty = $config['regular_count'] + $config['hots_easy_count'] + 
                      $config['hots_medium_count'] + $config['hots_hard_count'];

if ($total_by_type !== $total_by_difficulty) {
    $_SESSION['error'] = "Total soal berdasarkan jenis tidak sama dengan total berdasarkan tingkat kesulitan.";
    header("Location: configure_visual_generation.php");
    exit();
}

if ($config['visual_question_count'] > $total_by_type) {
    $_SESSION['error'] = "Jumlah soal visual tidak boleh melebihi total soal.";
    header("Location: configure_visual_generation.php");
    exit();
}

try {
    // Generate questions using Gemini API
    $geminiApi = new GeminiApi();
    $generated_questions = $geminiApi->generateVisualQuestions($rpp_data, $config);
    
    if (empty($generated_questions)) {
        throw new Exception("Tidak ada soal yang berhasil digenerate.");
    }

    // Initialize models
    $rppQuestion = new RppQuestion();
    $visualQuestion = new VisualQuestion();
    $visualTemplate = new VisualQuestionTemplate();
    
    $success_count = 0;
    $visual_count = 0;
    $errors = [];

    foreach ($generated_questions as $index => $question_data) {
        try {
            // Validate question data
            if (empty($question_data['question_text'])) {
                $errors[] = "Soal #" . ($index + 1) . ": Teks soal kosong";
                continue;
            }

            // Create base question
            $rppQuestion->rpp_id = $rpp_id;
            $rppQuestion->question_text = $question_data['question_text'];
            $rppQuestion->question_type = $question_data['question_type'];
            $rppQuestion->difficulty_level = $question_data['difficulty_level'] ?? 'regular';
            $rppQuestion->category = 'ai_generated_visual';
            $rppQuestion->source_type = 'ai_visual';

            // Handle options for multiple choice
            if ($question_data['question_type'] === 'multiple_choice') {
                $rppQuestion->options = json_encode($question_data['options'] ?? []);
                $rppQuestion->correct_answer = $question_data['correct_answer'] ?? 'A';
            } else {
                $rppQuestion->options = null;
                $rppQuestion->correct_answer = null;
            }

            // Set analysis data
            $rppQuestion->analysis_data = json_encode([
                'generated_by' => 'gemini_visual_ai',
                'generation_config' => $config,
                'has_visual' => $question_data['has_visual'] ?? false,
                'visual_type' => $question_data['visual_config']['type'] ?? null
            ]);

            $question_id = $rppQuestion->create();
            
            if (!$question_id) {
                $errors[] = "Soal #" . ($index + 1) . ": Gagal menyimpan ke database";
                continue;
            }

            $success_count++;

            // Create visual content if specified
            if (!empty($question_data['has_visual']) && !empty($question_data['visual_config'])) {
                try {
                    $visual_config = $question_data['visual_config'];
                    
                    // Generate complete HTML content
                    $html_content = $this->generateCompleteHtmlContent($visual_config);
                    
                    // Create visual question record
                    $visualQuestion->question_id = $question_id;
                    $visualQuestion->question_type = 'rpp_question';
                    $visualQuestion->template_id = null; // Will be set if template is used
                    $visualQuestion->visual_type = $visual_config['type'];
                    $visualQuestion->visual_config = $visual_config;
                    $visualQuestion->html_content = $html_content;
                    $visualQuestion->is_exportable = $config['include_visuals_in_export'];
                    $visualQuestion->generation_metadata = [
                        'generated_at' => date('Y-m-d H:i:s'),
                        'complexity' => $config['visual_complexity'],
                        'libraries' => $visual_config['libraries'] ?? []
                    ];

                    if ($visualQuestion->create()) {
                        $visual_count++;
                    } else {
                        $errors[] = "Soal #" . ($index + 1) . ": Gagal menyimpan konten visual";
                    }
                } catch (Exception $e) {
                    $errors[] = "Soal #" . ($index + 1) . ": Error visual - " . $e->getMessage();
                }
            }

        } catch (Exception $e) {
            $errors[] = "Soal #" . ($index + 1) . ": " . $e->getMessage();
        }
    }

    // Set session messages
    if ($success_count > 0) {
        $message = "Berhasil generate {$success_count} soal";
        if ($visual_count > 0) {
            $message .= " dengan {$visual_count} soal visual";
        }
        $_SESSION['success'] = $message;
    }

    if (!empty($errors)) {
        $_SESSION['warning'] = "Beberapa soal gagal: " . implode("; ", array_slice($errors, 0, 3));
        if (count($errors) > 3) {
            $_SESSION['warning'] .= " dan " . (count($errors) - 3) . " error lainnya.";
        }
    }

    // Redirect to questions list
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();

} catch (Exception $e) {
    $_SESSION['error'] = "Gagal generate soal: " . $e->getMessage();
    header("Location: configure_visual_generation.php");
    exit();
}

function generateCompleteHtmlContent($visual_config) {
    $html_template = $visual_config['html_template'] ?? '';
    $js_code = $visual_config['js_code'] ?? '';
    $libraries = $visual_config['libraries'] ?? [];
    
    // Build CDN links
    $cdn_links = [];
    foreach ($libraries as $library) {
        switch ($library) {
            case 'plotly.js':
                $cdn_links[] = '<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>';
                break;
            case 'mathjax':
                $cdn_links[] = '<script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>';
                $cdn_links[] = '<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>';
                break;
            case 'p5.js':
                $cdn_links[] = '<script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>';
                break;
            case '3dmol.js':
                $cdn_links[] = '<script src="https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.0.4/3Dmol-min.js"></script>';
                break;
            case 'handsontable':
                $cdn_links[] = '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">';
                $cdn_links[] = '<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>';
                break;
            case 'leaflet.js':
                $cdn_links[] = '<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">';
                $cdn_links[] = '<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>';
                break;
        }
    }
    
    $complete_html = '<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Question</title>
    <style>
        .visual-container { 
            width: 100%; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .export-btn {
            margin: 10px 0;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
    ' . implode("\n    ", $cdn_links) . '
</head>
<body>
    <div class="visual-container">
        ' . $html_template . '
        <button class="export-btn" onclick="exportToImage()">Export ke Gambar</button>
    </div>
    
    <script>
        ' . $js_code . '
        
        function exportToImage() {
            // Implementation for exporting visual to image
            // This will be enhanced based on the visual type
            console.log("Export functionality will be implemented");
        }
    </script>
</body>
</html>';
    
    return $complete_html;
}
?>
